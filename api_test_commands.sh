#!/bin/bash

# Django Depression Prediction API Test Commands
# Collection of cURL commands to test all API endpoints

# Configuration
BASE_URL="http://localhost:8000"
API_BASE="$BASE_URL/pred/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Function to test API endpoint
test_endpoint() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local headers="$5"
    
    echo -e "\n${YELLOW}Testing: $name${NC}"
    echo "URL: $url"
    echo "Method: $method"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" $headers "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" $headers -d "$data" "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "Response Code: $http_code"
    echo "Response Body:"
    echo "$body" | python3 -m json.tool 2>/dev/null || echo "$body"
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        print_success "Request successful"
    else
        print_error "Request failed"
    fi
    
    echo -e "${BLUE}----------------------------------------${NC}"
}

# Start testing
print_header "Django Depression Prediction API Tests"

print_info "Make sure your Django server is running on $BASE_URL"
print_info "Run: python manage.py runserver"
echo ""

# 1. Health Check
print_header "1. HEALTH CHECK"
test_endpoint \
    "Health Check" \
    "GET" \
    "$API_BASE/health/" \
    "" \
    ""

# 2. Authentication Tests
print_header "2. AUTHENTICATION TESTS"

# Login (you'll need to create a test user first)
print_info "Note: Create a test user first with: python manage.py createsuperuser"
test_endpoint \
    "Login" \
    "POST" \
    "$API_BASE/login/" \
    '{"username": "admin", "password": "admin123"}' \
    "-H 'Content-Type: application/json'"

# Logout
test_endpoint \
    "Logout" \
    "POST" \
    "$API_BASE/logout/" \
    "" \
    "-H 'Content-Type: application/json'"

# 3. Depression Prediction Tests
print_header "3. DEPRESSION PREDICTION TESTS"

# Sample prediction - Low risk profile
test_endpoint \
    "Predict Depression - Low Risk" \
    "POST" \
    "$API_BASE/predict/" \
    '{
        "gender": "1",
        "age": 28,
        "work_pressure": "2",
        "job_satisfaction": "5",
        "financial_stress": "1",
        "sleep_duration": "1",
        "dietary_habits": "0",
        "suicidal_thoughts": "0",
        "work_hours": 7,
        "family_history_of_mental_illness": "0"
    }' \
    "-H 'Content-Type: application/json'"

# Sample prediction - High risk profile
test_endpoint \
    "Predict Depression - High Risk" \
    "POST" \
    "$API_BASE/predict/" \
    '{
        "gender": "0",
        "age": 35,
        "work_pressure": "5",
        "job_satisfaction": "1",
        "financial_stress": "5",
        "sleep_duration": "2",
        "dietary_habits": "2",
        "suicidal_thoughts": "1",
        "work_hours": 12,
        "family_history_of_mental_illness": "1"
    }' \
    "-H 'Content-Type: application/json'"

# Sample prediction - Medium risk profile
test_endpoint \
    "Predict Depression - Medium Risk" \
    "POST" \
    "$API_BASE/predict/" \
    '{
        "gender": "1",
        "age": 30,
        "work_pressure": "3",
        "job_satisfaction": "3",
        "financial_stress": "3",
        "sleep_duration": "0",
        "dietary_habits": "1",
        "suicidal_thoughts": "0",
        "work_hours": 9,
        "family_history_of_mental_illness": "0"
    }' \
    "-H 'Content-Type: application/json'"

# 4. User Data Tests
print_header "4. USER DATA TESTS"

# Get user submissions (requires login)
test_endpoint \
    "Get User Submissions" \
    "GET" \
    "$API_BASE/submissions/" \
    "" \
    ""

# 5. Admin/Expert Tests
print_header "5. ADMIN/EXPERT TESTS"

# Dashboard statistics (requires admin/expert login)
test_endpoint \
    "Dashboard Statistics" \
    "GET" \
    "$API_BASE/dashboard/stats/" \
    "" \
    ""

# Reuse data (requires expert/admin login and existing submission)
test_endpoint \
    "Reuse Data" \
    "POST" \
    "$API_BASE/reuse-data/1/" \
    "" \
    "-H 'Content-Type: application/json'"

# 6. Error Handling Tests
print_header "6. ERROR HANDLING TESTS"

# Invalid JSON
test_endpoint \
    "Invalid JSON" \
    "POST" \
    "$API_BASE/predict/" \
    '{"invalid": json}' \
    "-H 'Content-Type: application/json'"

# Missing required fields
test_endpoint \
    "Missing Required Fields" \
    "POST" \
    "$API_BASE/predict/" \
    '{"gender": "1", "age": 25}' \
    "-H 'Content-Type: application/json'"

# Invalid field values
test_endpoint \
    "Invalid Field Values" \
    "POST" \
    "$API_BASE/predict/" \
    '{
        "gender": "invalid",
        "age": "not_a_number",
        "work_pressure": "10",
        "job_satisfaction": "4",
        "financial_stress": "2",
        "sleep_duration": "1",
        "dietary_habits": "0",
        "suicidal_thoughts": "0",
        "work_hours": 8,
        "family_history_of_mental_illness": "0"
    }' \
    "-H 'Content-Type: application/json'"

# 7. HTML Endpoints Tests (Original functionality)
print_header "7. HTML ENDPOINTS TESTS"

# Main form page
test_endpoint \
    "Main Form Page" \
    "GET" \
    "$BASE_URL/pred/" \
    "" \
    ""

# History page
test_endpoint \
    "History Page" \
    "GET" \
    "$BASE_URL/pred/history/" \
    "" \
    ""

# Admin dashboard
test_endpoint \
    "Admin Dashboard" \
    "GET" \
    "$BASE_URL/pred/admin/dashboard/" \
    "" \
    ""

print_header "API TESTING COMPLETED"
print_info "Check the responses above for any errors"
print_info "For authenticated endpoints, make sure to login first"
print_info "Some endpoints require specific user roles (admin/expert)"

echo -e "\n${GREEN}🎉 All API tests completed!${NC}"
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Create test users: python manage.py createsuperuser"
echo "2. Run the server: python manage.py runserver"
echo "3. Test with authentication by updating credentials in this script"
echo "4. Import the Postman collection for interactive testing"
