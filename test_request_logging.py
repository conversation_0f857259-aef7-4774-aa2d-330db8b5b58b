#!/usr/bin/env python3
"""
Test script to verify request logging is working
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"

def test_html_form_logging():
    """Test HTML form submission logging"""
    print("🧪 Testing HTML Form Request Logging")
    print("=" * 50)
    
    # Test data
    form_data = {
        'gender': '1',
        'age': 25,
        'work_pressure': '3',
        'job_satisfaction': '4',
        'financial_stress': '2',
        'sleep_duration': '1',
        'dietary_habits': '0',
        'suicidal_thoughts': '0',
        'work_hours': 8,
        'family_history_of_mental_illness': '0'
    }
    
    print("📤 Sending HTML form data...")
    print("Form data:", form_data)
    
    try:
        response = requests.post(
            f"{BASE_URL}/pred/",
            data=form_data,
            headers={'User-Agent': 'Test-Script/1.0'}
        )
        
        print(f"✅ Response Status: {response.status_code}")
        print("🔍 Check your Django server console for the request log!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Django server is running!")
        print("Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_api_logging():
    """Test API request logging"""
    print("\n🧪 Testing API Request Logging")
    print("=" * 50)
    
    # Test data
    json_data = {
        "gender": "1",
        "age": 25,
        "work_pressure": "3",
        "job_satisfaction": "4",
        "financial_stress": "2",
        "sleep_duration": "1",
        "dietary_habits": "0",
        "suicidal_thoughts": "0",
        "work_hours": 8,
        "family_history_of_mental_illness": "0"
    }
    
    print("📤 Sending API JSON data...")
    print("JSON data:", json.dumps(json_data, indent=2))
    
    try:
        response = requests.post(
            f"{BASE_URL}/pred/api/predict/",
            json=json_data,
            headers={'User-Agent': 'Test-Script-API/1.0'}
        )
        
        print(f"✅ Response Status: {response.status_code}")
        print("🔍 Check your Django server console for the API request log!")
        
        # Try to show response
        try:
            response_data = response.json()
            print("\n📥 Response Preview:")
            if response_data.get('success'):
                print(f"  Success: {response_data['success']}")
                print(f"  Message: {response_data['message']}")
                if 'data' in response_data and 'prediction' in response_data['data']:
                    pred = response_data['data']['prediction']
                    print(f"  Prediction: {pred.get('result')} ({pred.get('probability')}%)")
            else:
                print(f"  Error: {response_data['message']}")
        except:
            print("  (Could not parse JSON response)")
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Django server is running!")
        print("Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    print("🔍 Request Logging Test Suite")
    print("=" * 60)
    print("This script will send test requests to verify logging is working.")
    print("Make sure your Django server is running: python manage.py runserver")
    print()
    
    # Test HTML form logging
    test_html_form_logging()
    
    # Test API logging
    test_api_logging()
    
    print("\n" + "=" * 60)
    print("🎉 Test completed!")
    print("📋 What to check:")
    print("1. Look at your Django server console")
    print("2. You should see detailed request logs with:")
    print("   - Timestamp and request info")
    print("   - Raw POST/JSON data")
    print("   - Cleaned form data")
    print("   - Validation status")
    print()
    print("📝 Example log output:")
    print("=" * 60)
    print("🔍 FORM SUBMISSION REQUEST LOG")
    print("=" * 60)
    print("📅 Timestamp: 2024-01-15 10:30:00")
    print("🌐 Method: POST")
    print("📍 Path: /pred/")
    print("🖥️  User Agent: Test-Script/1.0")
    print("📡 Remote IP: 127.0.0.1")
    print()
    print("📦 Raw POST Data:")
    print("  gender: 1")
    print("  age: 25")
    print("  work_pressure: 3")
    print("  ...")
    print()
    print("✅ Form Validation: PASSED")
    print()
    print("🧹 Cleaned Form Data:")
    print("  gender: 1")
    print("  age: 25")
    print("  work_pressure: 3")
    print("  ...")
    print("=" * 60)

if __name__ == "__main__":
    main()
