#!/usr/bin/env python3
"""
Simple test for similarity analysis
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"

def test_form_submission_with_similarity():
    """Test form submission and check if similarity results are returned"""
    print("🧪 Testing Form Submission with Similarity Analysis")
    print("=" * 60)
    
    # Test data from your example
    form_data = {
        'gender': '1',
        'age': 20,
        'work_pressure': '2',
        'job_satisfaction': '1',
        'financial_stress': '4',
        'sleep_duration': '2',
        'dietary_habits': '1',
        'suicidal_thoughts': '1',
        'work_hours': 10,
        'family_history_of_mental_illness': '1'
    }
    
    print("📤 Submitting form data:")
    for key, value in form_data.items():
        print(f"  {key}: {value}")
    
    try:
        # Test HTML form submission
        print("\n🌐 Testing HTML Form Submission...")
        response = requests.post(
            f"{BASE_URL}/pred/",
            data=form_data,
            headers={'User-Agent': 'Similarity-Test/1.0'},
            allow_redirects=False  # Don't follow redirects to see the response
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 302:  # Redirect to results
            print("✅ Form submitted successfully (redirected to results)")
            
            # Follow the redirect to get results
            if 'Location' in response.headers:
                results_url = response.headers['Location']
                if not results_url.startswith('http'):
                    results_url = BASE_URL + results_url
                
                print(f"📄 Following redirect to: {results_url}")
                
                # Get the results page
                results_response = requests.get(results_url)
                if results_response.status_code == 200:
                    print("✅ Results page loaded successfully")
                    
                    # Check if similarity section is present
                    content = results_response.text
                    if 'similarity' in content.lower() or 'kemiripan' in content.lower():
                        print("✅ Similarity section found in results page")
                    else:
                        print("❌ Similarity section not found in results page")
                        
                    # Look for specific similarity indicators
                    if 'best_match' in content or 'kasus serupa' in content.lower():
                        print("✅ Best match information found")
                    else:
                        print("❌ Best match information not found")
                        
                else:
                    print(f"❌ Failed to load results page: {results_response.status_code}")
            else:
                print("❌ No redirect location found")
        else:
            print(f"❌ Form submission failed: {response.status_code}")
            print("Response:", response.text[:500])
    
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Django server is running!")
        print("Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_api_submission_with_similarity():
    """Test API submission and check similarity results"""
    print("\n🔌 Testing API Submission with Similarity Analysis")
    print("=" * 60)
    
    # Test data
    json_data = {
        "gender": "1",
        "age": 20,
        "work_pressure": "2",
        "job_satisfaction": "1",
        "financial_stress": "4",
        "sleep_duration": "2",
        "dietary_habits": "1",
        "suicidal_thoughts": "1",
        "work_hours": 10,
        "family_history_of_mental_illness": "1"
    }
    
    print("📤 Submitting API data:")
    print(json.dumps(json_data, indent=2))
    
    try:
        response = requests.post(
            f"{BASE_URL}/pred/api/predict/",
            json=json_data,
            headers={'User-Agent': 'Similarity-API-Test/1.0'}
        )
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("✅ API request successful")
                
                # Check for similarity data in response
                if 'data' in response_data and 'similarity' in response_data['data']:
                    similarity_data = response_data['data']['similarity']
                    print("✅ Similarity data found in API response")
                    
                    if similarity_data and similarity_data.get('success', False):
                        print("✅ Similarity calculation successful")
                        print(f"Best similarity: {similarity_data.get('best_similarity', 0):.2f}%")
                        print(f"Total cases: {similarity_data.get('total_cases', 0)}")
                        
                        if 'best_match' in similarity_data:
                            best_match = similarity_data['best_match']
                            print(f"Best match: {best_match.get('gender', 'N/A')}, Age {best_match.get('age', 'N/A')}")
                    else:
                        print("❌ Similarity calculation failed")
                        print(f"Error: {similarity_data.get('message', 'Unknown error')}")
                else:
                    print("❌ No similarity data in API response")
                    print("Available keys:", list(response_data.get('data', {}).keys()))
                
            except json.JSONDecodeError:
                print("❌ Could not parse JSON response")
                print("Response:", response.text[:500])
        else:
            print(f"❌ API request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('message', 'Unknown error')}")
            except:
                print("Response:", response.text[:500])
    
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Django server is running!")
        print("Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    """Run similarity tests"""
    print("🔍 Similarity Analysis Integration Test")
    print("=" * 70)
    print("Testing if similarity analysis is working in the web interface")
    print()
    
    print("📋 What this test checks:")
    print("1. Form submission includes similarity calculation")
    print("2. Results page displays similarity information")
    print("3. API returns similarity data")
    print("4. Similarity section is visible to users")
    print()
    
    # Test HTML form
    test_form_submission_with_similarity()
    
    # Test API
    test_api_submission_with_similarity()
    
    print("\n" + "=" * 70)
    print("🎉 Similarity integration testing completed!")
    print()
    print("📝 What to check:")
    print("1. 🔍 Django server console - Look for similarity calculation logs")
    print("2. 🌐 Web interface - Submit a form and check results page")
    print("3. 📊 Similarity section - Should show best match and similar cases")
    print("4. 🔌 API response - Should include similarity data")
    print()
    print("💡 If similarity is not showing:")
    print("- Check Django server logs for errors")
    print("- Verify dataset_processed.csv exists")
    print("- Check that pandas and sklearn are installed")
    print("- Look for error messages in similarity calculation")

if __name__ == "__main__":
    main()
