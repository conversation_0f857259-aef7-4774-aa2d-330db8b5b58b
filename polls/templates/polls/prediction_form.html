<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulir Prediksi Depresi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin-top: 50px;
            padding: 30px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .form-row {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        .form-group {
            flex: 0 0 48%; /* Adjust width for two columns */
            margin-bottom: 15px;
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }
        .form-group select,
        .form-group input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
        }
        .prediction-result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            font-size: 1.2em;
            font-weight: bold;
            text-align: center;
        }
        .text-danger {
            color: #dc3545; /* Bootstrap red */
        }
        .text-success {
            color: #28a745; /* Bootstrap green */
        }
        .btn-predict {
            background-color: #6a1b9a; /* Warna ungu gelap */
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease;
        }
        .btn-predict:hover {
            background-color: #4a148c; /* Warna ungu lebih gelap saat hover */
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="text-center mb-4">Formulir Prediksi Depresi</h2>
        <p class="text-center mb-4 text-muted">Masukkan data pasien untuk mendapatkan hasil prediksi.</p>

        <form method="post">
            {% csrf_token %} {# Penting untuk keamanan Django #}

            <div class="form-row">
                <div class="form-group">
                    {{ form.gender.label_tag }}
                    {{ form.gender }}
                </div>
                <div class="form-group">
                    {{ form.age.label_tag }}
                    {{ form.age }}
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    {{ form.work_pressure.label_tag }}
                    {{ form.work_pressure }}
                </div>
                <div class="form-group">
                    {{ form.job_satisfaction.label_tag }}
                    {{ form.job_satisfaction }}
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    {{ form.financial_stress.label_tag }}
                    {{ form.financial_stress }}
                </div>
                <div class="form-group">
                    {{ form.sleep_duration.label_tag }}
                    {{ form.sleep_duration }}
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    {{ form.dietary_habits.label_tag }}
                    {{ form.dietary_habits }}
                </div>
                <div class="form-group">
                    {{ form.suicidal_thoughts.label_tag }}
                    {{ form.suicidal_thoughts }}
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    {{ form.work_hours.label_tag }}
                    {{ form.work_hours }}
                </div>
                <div class="form-group">
                    {{ form.family_history_of_mental_illness.label_tag }}
                    {{ form.family_history_of_mental_illness }}
                </div>
            </div>

            {# Tampilkan error form secara keseluruhan, jika ada #}
            {% if form.errors %}
                <div class="alert alert-danger">
                    <strong>Error:</strong>
                    <ul>
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                <li>{{ field }}: {{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}

            <button type="submit" class="btn-predict">Prediksi</button>
        </form>

        {% if prediction_result %}
            <div class="prediction-result mt-4 {{ prediction_result.class }}">
                Hasil Prediksi: {{ prediction_result.text }}
                {% if prediction_result.proba_depresi %}
                    <br> (Probabilitas Depresi: {{ prediction_result.proba_depresi }})
                {% endif %}
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>