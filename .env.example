# Django Depression Prediction System - Environment Configuration
# Copy this file to .env and fill in your actual values

# Django Settings
DEBUG=False
SECRET_KEY=your-very-long-and-random-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com

# MySQL Database Configuration
DB_ENGINE=django.db.backends.mysql
DB_NAME=depression_prediction_db
DB_USER=django_user
DB_PASSWORD=your_secure_database_password
DB_HOST=localhost
DB_PORT=3306

# Security Settings (for production)
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# Email Configuration (optional)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
