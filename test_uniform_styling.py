#!/usr/bin/env python3
"""
Test script to verify uniform styling across all form fields
"""

import os
import sys
import django

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mysite.settings')
django.setup()

from pred.forms import MyForm

def test_uniform_styling():
    """Test that all radio button fields have consistent styling"""
    print("🎨 Testing Uniform Form Field Styling")
    print("=" * 50)
    
    form = MyForm()
    
    # All radio button fields
    radio_fields = [
        'gender',
        'work_pressure', 
        'job_satisfaction',
        'financial_stress',
        'sleep_duration',
        'dietary_habits',
        'suicidal_thoughts',
        'family_history_of_mental_illness'
    ]
    
    print("📋 Radio Button Field Analysis:")
    print("-" * 30)
    
    for field_name in radio_fields:
        field = form.fields[field_name]
        widget_type = field.widget.__class__.__name__
        choices = field.choices
        choice_count = len(choices)
        
        # Determine layout category
        if choice_count == 2:
            layout_category = "two-options"
        elif choice_count >= 4:
            layout_category = "many-options"
        else:
            layout_category = "standard"
        
        print(f"✅ {field_name}:")
        print(f"   Widget: {widget_type}")
        print(f"   Options: {choice_count}")
        print(f"   Category: {layout_category}")
        print(f"   Styling: Uniform with other {layout_category} fields")
        print()
    
    # Test that Gender field is now treated like other two-option fields
    gender_field = form.fields['gender']
    suicidal_field = form.fields['suicidal_thoughts']
    family_field = form.fields['family_history_of_mental_illness']
    
    # All should be two-option fields with same widget type
    two_option_fields = [
        ('gender', gender_field),
        ('suicidal_thoughts', suicidal_field),
        ('family_history_of_mental_illness', family_field)
    ]
    
    print("🔍 Two-Option Fields Consistency Check:")
    print("-" * 40)
    
    all_consistent = True
    for field_name, field in two_option_fields:
        widget_type = field.widget.__class__.__name__
        choice_count = len(field.choices)
        
        if widget_type == 'RadioSelect' and choice_count == 2:
            print(f"✅ {field_name}: Consistent (RadioSelect, 2 options)")
        else:
            print(f"❌ {field_name}: Inconsistent ({widget_type}, {choice_count} options)")
            all_consistent = False
    
    return all_consistent

def test_form_validation():
    """Test that form validation still works with uniform styling"""
    print("\n🧪 Testing Form Validation")
    print("=" * 30)
    
    test_data = {
        'gender': '1',
        'age': 25,
        'work_pressure': '3',
        'job_satisfaction': '4',
        'financial_stress': '2',
        'sleep_duration': '1',
        'dietary_habits': '0',
        'suicidal_thoughts': '0',
        'work_hours': 8,
        'family_history_of_mental_illness': '0'
    }
    
    form = MyForm(data=test_data)
    if form.is_valid():
        print("✅ Form validation works correctly with uniform styling")
        return True
    else:
        print("❌ Form validation failed")
        print("Errors:", form.errors)
        return False

def main():
    """Run uniform styling tests"""
    print("🎨 Uniform Form Styling Test Suite")
    print("=" * 60)
    print("Testing that Gender field now matches other labels")
    print()
    
    try:
        # Test uniform styling
        styling_ok = test_uniform_styling()
        
        # Test form validation
        validation_ok = test_form_validation()
        
        print("\n" + "=" * 60)
        if styling_ok and validation_ok:
            print("🎉 Gender field successfully unified with other labels!")
            print()
            print("✅ Key Changes:")
            print("   • Gender field no longer has special styling")
            print("   • Gender field uses same layout as other two-option fields")
            print("   • Consistent blue/purple theme across all fields")
            print("   • Uniform spacing and typography")
            print("   • Same hover and selection effects")
            print()
            print("📋 Current Field Categories:")
            print("   • Two-Option Fields: Gender, Suicidal Thoughts, Family History")
            print("   • Many-Option Fields: Work Pressure, Job Satisfaction, Financial Stress, Sleep Duration")
            print("   • Standard Fields: Dietary Habits")
            print("   • Number Input Fields: Age, Work Hours")
            print()
            print("🎨 Visual Consistency:")
            print("   • All radio buttons use same base styling")
            print("   • Category-based layout (two-options, many-options, standard)")
            print("   • Consistent blue/purple color scheme")
            print("   • Uniform animations and interactions")
            
            return True
        else:
            print("❌ Some uniform styling tests failed!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
