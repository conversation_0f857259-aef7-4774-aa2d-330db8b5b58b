#!/usr/bin/env python3
"""
Test script to verify data transformation for ML model
"""

import os
import sys
import django
import pandas as pd

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mysite.settings')
django.setup()

from pred.views import transform_form_data_for_model

def test_data_transformation():
    """Test the data transformation function"""
    print("🧪 Testing Data Transformation for ML Model")
    print("=" * 60)
    
    # Test case 1: Male user with high risk profile
    print("\n📋 Test Case 1: Male, High Risk Profile")
    user_data_1 = {
        'gender': '1',  # Male
        'age': 35,
        'work_pressure': '5',
        'job_satisfaction': '1',
        'financial_stress': '5',
        'sleep_duration': '2',
        'dietary_habits': '2',
        'suicidal_thoughts': '1',
        'work_hours': 12,
        'family_history_of_mental_illness': '1'
    }
    
    print("Original user data:")
    for key, value in user_data_1.items():
        print(f"  {key}: {value}")
    
    transformed_1 = transform_form_data_for_model(user_data_1)
    print("\nTransformed data for model:")
    for key, value in transformed_1.items():
        print(f"  {key}: {value}")
    
    # Verify gender transformation
    assert transformed_1['gender_Male'] == 1, "Male should have gender_Male = 1"
    assert transformed_1['gender_Female'] == 0, "Male should have gender_Female = 0"
    print("✅ Gender transformation correct for Male")
    
    # Test case 2: Female user with low risk profile
    print("\n📋 Test Case 2: Female, Low Risk Profile")
    user_data_2 = {
        'gender': '0',  # Female
        'age': 28,
        'work_pressure': '2',
        'job_satisfaction': '5',
        'financial_stress': '1',
        'sleep_duration': '1',
        'dietary_habits': '0',
        'suicidal_thoughts': '0',
        'work_hours': 7,
        'family_history_of_mental_illness': '0'
    }
    
    print("Original user data:")
    for key, value in user_data_2.items():
        print(f"  {key}: {value}")
    
    transformed_2 = transform_form_data_for_model(user_data_2)
    print("\nTransformed data for model:")
    for key, value in transformed_2.items():
        print(f"  {key}: {value}")
    
    # Verify gender transformation
    assert transformed_2['gender_Male'] == 0, "Female should have gender_Male = 0"
    assert transformed_2['gender_Female'] == 1, "Female should have gender_Female = 1"
    print("✅ Gender transformation correct for Female")
    
    # Test case 3: Create DataFrame (as the model expects)
    print("\n📋 Test Case 3: DataFrame Creation")
    df_1 = pd.DataFrame([transformed_1])
    df_2 = pd.DataFrame([transformed_2])
    
    print("DataFrame for Male user:")
    print(df_1)
    print("\nDataFrame for Female user:")
    print(df_2)
    
    # Verify DataFrame structure
    expected_columns = [
        'age', 'work_pressure', 'job_satisfaction', 'financial_stress',
        'sleep_duration', 'dietary_habits', 'suicidal_thoughts', 'work_hours',
        'family_history_of_mental_illness', 'gender_Female', 'gender_Male'
    ]
    
    for col in expected_columns:
        assert col in df_1.columns, f"Missing column: {col}"
        assert col in df_2.columns, f"Missing column: {col}"
    
    print("✅ DataFrame structure correct")
    
    # Test case 4: Compare with expected format
    print("\n📋 Test Case 4: Compare with Expected Format")
    expected_format = {
        "age": 40,
        "work_pressure": 5,
        "job_satisfaction": 3,
        "financial_stress": 5,
        "sleep_duration": 3,
        "dietary_habits": 2,
        "suicidal_thoughts": 0,
        "work_hours": 9,
        "family_history_of_mental_illness": 1,
        "gender_Female": 0,
        "gender_Male": 1
    }
    
    print("Expected format from your example:")
    for key, value in expected_format.items():
        print(f"  {key}: {value}")
    
    # Create test data that matches the expected format
    test_user_data = {
        'gender': '1',  # Male (should become gender_Male=1, gender_Female=0)
        'age': 40,
        'work_pressure': '5',
        'job_satisfaction': '3',
        'financial_stress': '5',
        'sleep_duration': '3',
        'dietary_habits': '2',
        'suicidal_thoughts': '0',
        'work_hours': 9,
        'family_history_of_mental_illness': '1'
    }
    
    transformed_test = transform_form_data_for_model(test_user_data)
    print("\nOur transformation result:")
    for key, value in transformed_test.items():
        print(f"  {key}: {value}")
    
    # Verify it matches the expected format
    for key, expected_value in expected_format.items():
        actual_value = transformed_test[key]
        assert actual_value == expected_value, f"Mismatch for {key}: expected {expected_value}, got {actual_value}"
    
    print("✅ Transformation matches expected format perfectly!")
    
    return True

def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n🔍 Testing Edge Cases")
    print("=" * 40)
    
    # Test with string numbers (as they come from forms)
    edge_case_data = {
        'gender': '0',  # String instead of int
        'age': '25',    # String instead of int
        'work_pressure': '3',
        'job_satisfaction': '4',
        'financial_stress': '2',
        'sleep_duration': '1',
        'dietary_habits': '0',
        'suicidal_thoughts': '0',
        'work_hours': '8',  # String instead of int
        'family_history_of_mental_illness': '0'
    }
    
    print("Testing with string values (as from HTML forms):")
    for key, value in edge_case_data.items():
        print(f"  {key}: '{value}' (type: {type(value).__name__})")
    
    try:
        transformed = transform_form_data_for_model(edge_case_data)
        print("\nTransformation successful:")
        for key, value in transformed.items():
            print(f"  {key}: {value} (type: {type(value).__name__})")
        print("✅ String to int conversion works correctly")
        return True
    except Exception as e:
        print(f"❌ Error in transformation: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🔄 Data Transformation Test Suite")
    print("=" * 70)
    print("Testing conversion from user request format to ML model format")
    print()
    
    try:
        # Test basic transformation
        success1 = test_data_transformation()
        
        # Test edge cases
        success2 = test_edge_cases()
        
        if success1 and success2:
            print("\n" + "=" * 70)
            print("🎉 All tests passed!")
            print("✅ Data transformation is working correctly")
            print("✅ User request body will be properly converted for ML model")
            print()
            print("📊 Summary:")
            print("- User sends: {'gender': '1', 'age': 20, ...}")
            print("- Model gets: {'gender_Male': 1, 'gender_Female': 0, 'age': 20, ...}")
            print("- All data types are properly converted to integers")
            print("- Gender field is properly one-hot encoded")
            return True
        else:
            print("\n❌ Some tests failed!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
