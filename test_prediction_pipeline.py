#!/usr/bin/env python3
"""
Test the complete prediction pipeline with the new data transformation
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"

def test_html_form_prediction():
    """Test HTML form submission with the new data transformation"""
    print("🧪 Testing HTML Form Prediction Pipeline")
    print("=" * 60)
    
    # Test data that matches your example
    form_data = {
        'gender': '1',  # Male
        'age': 20,
        'work_pressure': '2',
        'job_satisfaction': '1',
        'financial_stress': '4',
        'sleep_duration': '2',
        'dietary_habits': '1',
        'suicidal_thoughts': '1',
        'work_hours': 10,
        'family_history_of_mental_illness': '1'
    }
    
    print("📤 Sending form data:")
    for key, value in form_data.items():
        print(f"  {key}: {value}")
    
    print("\n🔍 Expected transformation:")
    print("  gender: '1' → gender_Male: 1, gender_Female: 0")
    print("  Other fields: converted to integers")
    
    try:
        response = requests.post(
            f"{BASE_URL}/pred/",
            data=form_data,
            headers={'User-Agent': 'Test-Pipeline/1.0'}
        )
        
        print(f"\n✅ Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("🎉 Form submission successful!")
            print("🔍 Check your Django server console for:")
            print("  - Raw POST data log")
            print("  - Data transformation log")
            print("  - Final DataFrame for model")
            print("  - Prediction result")
        else:
            print("❌ Form submission failed")
            print("Response:", response.text[:500])
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Django server is running!")
        print("Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_api_prediction():
    """Test API prediction with the new data transformation"""
    print("\n🧪 Testing API Prediction Pipeline")
    print("=" * 60)
    
    # Same test data as JSON
    json_data = {
        "gender": "1",  # Male
        "age": 20,
        "work_pressure": "2",
        "job_satisfaction": "1",
        "financial_stress": "4",
        "sleep_duration": "2",
        "dietary_habits": "1",
        "suicidal_thoughts": "1",
        "work_hours": 10,
        "family_history_of_mental_illness": "1"
    }
    
    print("📤 Sending JSON data:")
    print(json.dumps(json_data, indent=2))
    
    try:
        response = requests.post(
            f"{BASE_URL}/pred/api/predict/",
            json=json_data,
            headers={'User-Agent': 'Test-API-Pipeline/1.0'}
        )
        
        print(f"\n✅ Response Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("🎉 API prediction successful!")
                print("\n📥 Response data:")
                print(f"  Success: {response_data.get('success')}")
                print(f"  Message: {response_data.get('message')}")
                
                if 'data' in response_data:
                    data = response_data['data']
                    if 'prediction' in data:
                        pred = data['prediction']
                        print(f"\n🧠 Prediction Result:")
                        print(f"  Result: {pred.get('result')}")
                        print(f"  Probability: {pred.get('probability')}%")
                        print(f"  Message: {pred.get('message')}")
                    
                    if 'form_data_display' in data:
                        print(f"\n📊 Form Data Display (Indonesian):")
                        display_data = data['form_data_display']
                        for key, value in display_data.items():
                            print(f"  {key}: {value}")
                
                print("\n🔍 Check your Django server console for:")
                print("  - API request log")
                print("  - Data transformation log")
                print("  - Final DataFrame for model")
                
            except json.JSONDecodeError:
                print("❌ Could not parse JSON response")
                print("Response:", response.text[:500])
        else:
            print("❌ API prediction failed")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('message')}")
                if 'errors' in error_data:
                    print("Validation errors:", error_data['errors'])
            except:
                print("Response:", response.text[:500])
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure Django server is running!")
        print("Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_different_profiles():
    """Test with different risk profiles"""
    print("\n🧪 Testing Different Risk Profiles")
    print("=" * 60)
    
    profiles = [
        {
            "name": "Low Risk Female",
            "data": {
                "gender": "0",  # Female
                "age": 25,
                "work_pressure": "1",
                "job_satisfaction": "5",
                "financial_stress": "1",
                "sleep_duration": "1",
                "dietary_habits": "0",
                "suicidal_thoughts": "0",
                "work_hours": 6,
                "family_history_of_mental_illness": "0"
            }
        },
        {
            "name": "High Risk Male",
            "data": {
                "gender": "1",  # Male
                "age": 45,
                "work_pressure": "5",
                "job_satisfaction": "1",
                "financial_stress": "5",
                "sleep_duration": "2",
                "dietary_habits": "2",
                "suicidal_thoughts": "1",
                "work_hours": 14,
                "family_history_of_mental_illness": "1"
            }
        }
    ]
    
    for profile in profiles:
        print(f"\n📋 Testing: {profile['name']}")
        print("Data:", json.dumps(profile['data'], indent=2))
        
        try:
            response = requests.post(
                f"{BASE_URL}/pred/api/predict/",
                json=profile['data'],
                headers={'User-Agent': f'Test-{profile["name"].replace(" ", "-")}/1.0'}
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if 'data' in response_data and 'prediction' in response_data['data']:
                    pred = response_data['data']['prediction']
                    print(f"  Result: {pred.get('result')} ({pred.get('probability')}%)")
                else:
                    print("  ✅ Request successful (check server logs)")
            else:
                print(f"  ❌ Failed with status {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error: {str(e)}")

def main():
    """Run all pipeline tests"""
    print("🔄 Complete Prediction Pipeline Test")
    print("=" * 70)
    print("Testing the complete flow from user input to ML prediction")
    print("with the new data transformation for Joblib model")
    print()
    
    print("📋 What this test covers:")
    print("1. User sends: {'gender': '1', 'age': 20, ...}")
    print("2. Backend transforms to: {'gender_Male': 1, 'gender_Female': 0, 'age': 20, ...}")
    print("3. ML model receives properly formatted DataFrame")
    print("4. Prediction is made and returned to user")
    print()
    
    # Test HTML form
    test_html_form_prediction()
    
    # Test API
    test_api_prediction()
    
    # Test different profiles
    test_different_profiles()
    
    print("\n" + "=" * 70)
    print("🎉 Pipeline testing completed!")
    print()
    print("📝 What to check in Django server console:")
    print("1. 🔍 REQUEST LOG - Shows original user data")
    print("2. 🔄 DATA TRANSFORMATION - Shows gender conversion")
    print("3. 📊 FINAL DATAFRAME - Shows data ready for model")
    print("4. 🧠 PREDICTION RESULT - Shows ML model output")
    print()
    print("✅ If you see all these logs, the pipeline is working correctly!")
    print("✅ Your Joblib model should now receive properly formatted data!")

if __name__ == "__main__":
    main()
