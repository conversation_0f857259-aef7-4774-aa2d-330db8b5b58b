#!/usr/bin/env python3
"""
Test script to verify radio button form functionality
"""

import os
import sys
import django

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mysite.settings')
django.setup()

from pred.forms import MyForm

def test_radio_button_form():
    """Test that the form works with radio button inputs"""
    print("🔘 Testing Radio Button Form")
    print("=" * 40)
    
    # Test data that should be valid
    test_data = {
        'gender': '1',
        'age': 25,
        'work_pressure': '3',
        'job_satisfaction': '4',
        'financial_stress': '2',
        'sleep_duration': '1',
        'dietary_habits': '0',
        'suicidal_thoughts': '0',
        'work_hours': 8,
        'family_history_of_mental_illness': '0'
    }
    
    print("📝 Testing form with data:")
    for key, value in test_data.items():
        print(f"  {key}: {value}")
    
    # Create form instance
    form = MyForm(data=test_data)
    
    # Check if form is valid
    if form.is_valid():
        print("\n✅ Form validation successful!")
        print("📋 Cleaned data:")
        for key, value in form.cleaned_data.items():
            print(f"  {key}: {value}")
        return True
    else:
        print("\n❌ Form validation failed!")
        print("🚨 Errors:")
        for field, errors in form.errors.items():
            print(f"  {field}: {errors}")
        return False

def test_form_widgets():
    """Test that the form widgets are correctly set"""
    print("\n🎛️ Testing Form Widget Types")
    print("=" * 40)
    
    form = MyForm()
    
    # Check widget types
    widget_tests = [
        ('gender', 'RadioSelect'),
        ('age', 'NumberInput'),
        ('work_pressure', 'RadioSelect'),
        ('job_satisfaction', 'RadioSelect'),
        ('financial_stress', 'RadioSelect'),
        ('sleep_duration', 'RadioSelect'),
        ('dietary_habits', 'RadioSelect'),
        ('suicidal_thoughts', 'RadioSelect'),
        ('work_hours', 'NumberInput'),
        ('family_history_of_mental_illness', 'RadioSelect'),
    ]
    
    all_correct = True
    for field_name, expected_widget in widget_tests:
        field = form.fields[field_name]
        actual_widget = field.widget.__class__.__name__
        
        if actual_widget == expected_widget:
            print(f"✅ {field_name}: {actual_widget}")
        else:
            print(f"❌ {field_name}: Expected {expected_widget}, got {actual_widget}")
            all_correct = False
    
    return all_correct

def test_choice_options():
    """Test that choice fields have the correct options"""
    print("\n📋 Testing Choice Options")
    print("=" * 40)
    
    form = MyForm()
    
    # Test that empty options are removed (for radio buttons)
    choice_fields = ['gender', 'work_pressure', 'job_satisfaction', 'financial_stress', 
                    'sleep_duration', 'dietary_habits', 'suicidal_thoughts', 
                    'family_history_of_mental_illness']
    
    all_correct = True
    for field_name in choice_fields:
        field = form.fields[field_name]
        choices = field.choices
        
        # Check that no empty choice exists
        has_empty = any(choice[0] == '' for choice in choices)
        
        if not has_empty:
            print(f"✅ {field_name}: No empty default option (good for radio buttons)")
            print(f"   Options: {[choice[1] for choice in choices]}")
        else:
            print(f"❌ {field_name}: Still has empty default option")
            all_correct = False
    
    return all_correct

def main():
    """Run all radio button tests"""
    print("🔘 Radio Button Form Test Suite")
    print("=" * 50)
    print("Testing the updated form with radio button inputs")
    print()
    
    try:
        # Test form validation
        validation_ok = test_radio_button_form()
        
        # Test widget types
        widgets_ok = test_form_widgets()
        
        # Test choice options
        choices_ok = test_choice_options()
        
        print("\n" + "=" * 50)
        if validation_ok and widgets_ok and choices_ok:
            print("🎉 All radio button tests passed!")
            print("✅ Form validation works correctly")
            print("✅ Widget types are correctly set")
            print("✅ Choice options are properly configured")
            print("✅ Age and Work Hours remain as number inputs")
            print("✅ All other fields are now radio buttons")
            
            print("\n📋 Summary of Changes:")
            print("- Gender: Radio buttons (Laki-laki, Perempuan)")
            print("- Age: Number input (unchanged)")
            print("- Work Pressure: Radio buttons (5 levels)")
            print("- Job Satisfaction: Radio buttons (5 levels)")
            print("- Financial Stress: Radio buttons (5 levels)")
            print("- Sleep Duration: Radio buttons (4 options)")
            print("- Dietary Habits: Radio buttons (3 options)")
            print("- Suicidal Thoughts: Radio buttons (Ya, Tidak)")
            print("- Work Hours: Number input (unchanged)")
            print("- Family History: Radio buttons (Ya, Tidak)")
            
            return True
        else:
            print("❌ Some radio button tests failed!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
