# 🚀 Django Depression Prediction API Documentation

Complete API documentation for the Django Depression Prediction System.

## 📋 **Table of Contents**

1. [Overview](#overview)
2. [Base URL](#base-url)
3. [Authentication](#authentication)
4. [API Endpoints](#api-endpoints)
5. [Data Models](#data-models)
6. [Error Handling](#error-handling)
7. [Testing](#testing)

## 🎯 **Overview**

The Django Depression Prediction API provides REST endpoints for:
- Depression risk prediction using machine learning
- User authentication and session management
- Submission history and data management
- Admin/Expert dashboard functionality
- Data reuse for dataset enhancement

## 🌐 **Base URL**

```
http://localhost:8000/pred/api/
```

## 🔐 **Authentication**

The API uses Django's session-based authentication. Most endpoints require authentication.

### Login
```http
POST /pred/api/login/
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

### Logout
```http
POST /pred/api/logout/
```

## 📡 **API Endpoints**

### 1. Health Check

**GET** `/pred/api/health/`

Check API health status.

**Response:**
```json
{
    "success": true,
    "message": "Sistem berjalan normal",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "status": "healthy",
        "database": "OK",
        "timestamp": "2024-01-15T10:30:00.000Z",
        "version": "1.0.0"
    }
}
```

### 2. Depression Prediction

**POST** `/pred/api/predict/`

Predict depression risk based on user input.

**Request Body:**
```json
{
    "gender": "1",                              // "1" = Male, "0" = Female
    "age": 25,                                  // Integer: Age in years
    "work_pressure": "3",                       // "1"-"5": Very Low to Very High
    "job_satisfaction": "4",                    // "1"-"5": Very Low to Very High
    "financial_stress": "2",                    // "1"-"5": Very Low to Very High
    "sleep_duration": "1",                      // "0"=5-6h, "1"=7-8h, "2"=<5h, "3"=>8h
    "dietary_habits": "0",                      // "0"=Healthy, "1"=Moderate, "2"=Unhealthy
    "suicidal_thoughts": "0",                   // "1" = Yes, "0" = No
    "work_hours": 8,                           // Integer: Hours per day
    "family_history_of_mental_illness": "0"    // "1" = Yes, "0" = No
}
```

**Response:**
```json
{
    "success": true,
    "message": "Prediksi berhasil dilakukan",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "submission_id": 123,
        "prediction": {
            "result": "Negatif",
            "probability": 85.5,
            "message": "Model menunjukkan kemungkinan depresi yang lebih rendah..."
        },
        "similarity": {
            "success": true,
            "best_similarity": 78.5,
            "best_match": {
                "gender": "Laki-laki",
                "age": 26,
                "work_pressure": "Sedang",
                // ... other fields
            },
            "top_matches": [...],
            "total_cases": 1000
        },
        "form_data_display": {
            "jenis_kelamin": "Laki-laki",
            "usia": 25,
            "tekanan_kerja": "Sedang",
            // ... other fields in Indonesian
        },
        "submitted_at": "2024-01-15T10:30:00.000Z"
    }
}
```

### 3. User Submissions

**GET** `/pred/api/submissions/`

Get user's submission history. Requires authentication.

**Response:**
```json
{
    "success": true,
    "message": "Data riwayat berhasil diambil",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "submissions": [
            {
                "id": 123,
                "form_data": {
                    "jenis_kelamin": "Laki-laki",
                    "usia": 25,
                    // ... other fields
                },
                "prediction": {
                    "result": "Negatif",
                    "probability": 85.5,
                    "message": "..."
                },
                "similarity": {
                    "score": 78.5,
                    "similar_case_id": 456
                },
                "reuse_info": {
                    "is_reused": false,
                    "reused_at": null,
                    "reused_by": null
                },
                "submitted_at": "2024-01-15T10:30:00.000Z"
            }
        ],
        "total_count": 1,
        "user_role": "user"
    }
}
```

### 4. Dashboard Statistics

**GET** `/pred/api/dashboard/stats/`

Get dashboard statistics. Requires admin/expert role.

**Response:**
```json
{
    "success": true,
    "message": "Statistik dashboard berhasil diambil",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "statistics": {
            "total_submissions": 150,
            "total_users": 25,
            "reused_submissions": 12
        },
        "recent_submissions": [
            {
                "id": 123,
                "gender": "Laki-laki",
                "age": 25,
                "prediction_result": "Negatif",
                "submitted_at": "2024-01-15T10:30:00.000Z"
            }
        ],
        "user_role": "admin"
    }
}
```

### 5. Reuse Data

**POST** `/pred/api/reuse-data/{submission_id}/`

Mark submission data for reuse in dataset. Requires expert/admin role.

**Response:**
```json
{
    "success": true,
    "message": "Berhasil menambahkan data pengiriman #123 ke dataset",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "submission_id": 123,
        "reused_at": "2024-01-15T10:30:00.000Z",
        "reused_by": "expert_user"
    }
}
```

## 📊 **Data Models**

### Form Input Values

| Field | Type | Values | Description |
|-------|------|--------|-------------|
| gender | string | "1", "0" | 1=Male, 0=Female |
| age | integer | 18-100 | Age in years |
| work_pressure | string | "1"-"5" | 1=Very Low, 5=Very High |
| job_satisfaction | string | "1"-"5" | 1=Very Low, 5=Very High |
| financial_stress | string | "1"-"5" | 1=Very Low, 5=Very High |
| sleep_duration | string | "0"-"3" | 0=5-6h, 1=7-8h, 2=<5h, 3=>8h |
| dietary_habits | string | "0"-"2" | 0=Healthy, 1=Moderate, 2=Unhealthy |
| suicidal_thoughts | string | "1", "0" | 1=Yes, 0=No |
| work_hours | integer | 1-24 | Hours per day |
| family_history_of_mental_illness | string | "1", "0" | 1=Yes, 0=No |

### Display Values (Indonesian)

| Database Value | Display Value |
|----------------|---------------|
| Gender "1" | "Laki-laki" |
| Gender "0" | "Perempuan" |
| Pressure "1" | "Sangat Rendah" |
| Pressure "2" | "Rendah" |
| Pressure "3" | "Sedang" |
| Pressure "4" | "Tinggi" |
| Pressure "5" | "Sangat Tinggi" |
| Sleep "0" | "5-6 Jam" |
| Sleep "1" | "7-8 Jam" |
| Sleep "2" | "Kurang dari 5 Jam" |
| Sleep "3" | "Lebih dari 8 Jam" |
| Diet "0" | "Sehat" |
| Diet "1" | "Sedang" |
| Diet "2" | "Tidak Sehat" |
| Yes/No "1" | "Ya" |
| Yes/No "0" | "Tidak" |

## ❌ **Error Handling**

### Standard Error Response
```json
{
    "success": false,
    "message": "Error description in Indonesian",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "errors": {
        "field_name": ["Error details"]
    }
}
```

### Common HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request (validation errors) |
| 401 | Unauthorized (login required) |
| 403 | Forbidden (insufficient permissions) |
| 404 | Not Found |
| 500 | Internal Server Error |

### Example Error Responses

**Validation Error (400):**
```json
{
    "success": false,
    "message": "Data validasi gagal",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "errors": {
        "age": ["This field is required."],
        "gender": ["Select a valid choice."]
    }
}
```

**Authentication Error (401):**
```json
{
    "success": false,
    "message": "Username atau password salah",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "errors": {}
}
```

**Permission Error (403):**
```json
{
    "success": false,
    "message": "Akses ditolak - hanya untuk Expert/Admin",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "errors": {}
}
```

## 🧪 **Testing**

### Using Postman

1. Import the collection: `Django_Depression_Prediction_API.postman_collection.json`
2. Set the base URL variable: `http://localhost:8000`
3. Create a test user: `python manage.py createsuperuser`
4. Test authentication endpoints first
5. Use the session cookie for subsequent requests

### Using cURL

Run the test script:
```bash
./api_test_commands.sh
```

### Manual Testing Examples

**Health Check:**
```bash
curl -X GET http://localhost:8000/pred/api/health/
```

**Login:**
```bash
curl -X POST http://localhost:8000/pred/api/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

**Predict Depression:**
```bash
curl -X POST http://localhost:8000/pred/api/predict/ \
  -H "Content-Type: application/json" \
  -d '{
    "gender": "1",
    "age": 25,
    "work_pressure": "3",
    "job_satisfaction": "4",
    "financial_stress": "2",
    "sleep_duration": "1",
    "dietary_habits": "0",
    "suicidal_thoughts": "0",
    "work_hours": 8,
    "family_history_of_mental_illness": "0"
  }'
```

## 🚀 **Getting Started**

1. **Start the Django server:**
   ```bash
   python manage.py runserver
   ```

2. **Create a test user:**
   ```bash
   python manage.py createsuperuser
   ```

3. **Test the API:**
   - Import Postman collection
   - Run cURL test script
   - Or use manual cURL commands

4. **Check API health:**
   ```bash
   curl http://localhost:8000/pred/api/health/
   ```

## 📝 **Notes**

- All responses include Indonesian text for user-facing messages
- Database stores numeric values for efficiency
- ML model receives proper numeric inputs
- Session-based authentication is used
- CSRF protection is disabled for API endpoints
- All timestamps are in ISO 8601 format
- Error messages are in Indonesian for consistency

## 🎯 **Next Steps**

- Add API versioning (`/v1/`)
- Implement rate limiting
- Add API key authentication option
- Create OpenAPI/Swagger documentation
- Add more detailed logging
- Implement caching for better performance
