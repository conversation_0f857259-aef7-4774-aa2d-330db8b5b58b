{"info": {"_postman_id": "django-depression-api-collection", "name": "Django Depression Prediction API", "description": "Complete API collection for testing Django Depression Prediction System endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "api_base", "value": "{{base_url}}/pred/api", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/health/", "host": ["{{api_base}}"], "path": ["health", ""]}}, "response": []}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Store session cookie for subsequent requests", "if (pm.response.code === 200) {", "    const cookies = pm.response.headers.get('Set-Cookie');", "    if (cookies) {", "        pm.globals.set('session_cookie', cookies);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"password\": \"testpassword123\"\n}"}, "url": {"raw": "{{api_base}}/login/", "host": ["{{api_base}}"], "path": ["login", ""]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{api_base}}/logout/", "host": ["{{api_base}}"], "path": ["logout", ""]}}, "response": []}]}, {"name": "Depression Prediction", "item": [{"name": "Predict Depression - Sample Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"gender\": \"1\",\n    \"age\": 25,\n    \"work_pressure\": \"3\",\n    \"job_satisfaction\": \"4\",\n    \"financial_stress\": \"2\",\n    \"sleep_duration\": \"1\",\n    \"dietary_habits\": \"0\",\n    \"suicidal_thoughts\": \"0\",\n    \"work_hours\": 8,\n    \"family_history_of_mental_illness\": \"0\"\n}"}, "url": {"raw": "{{api_base}}/predict/", "host": ["{{api_base}}"], "path": ["predict", ""]}}, "response": []}, {"name": "Predict Depression - High Risk Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"gender\": \"0\",\n    \"age\": 35,\n    \"work_pressure\": \"5\",\n    \"job_satisfaction\": \"1\",\n    \"financial_stress\": \"5\",\n    \"sleep_duration\": \"2\",\n    \"dietary_habits\": \"2\",\n    \"suicidal_thoughts\": \"1\",\n    \"work_hours\": 12,\n    \"family_history_of_mental_illness\": \"1\"\n}"}, "url": {"raw": "{{api_base}}/predict/", "host": ["{{api_base}}"], "path": ["predict", ""]}}, "response": []}, {"name": "Predict Depression - Low Risk Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"gender\": \"1\",\n    \"age\": 28,\n    \"work_pressure\": \"2\",\n    \"job_satisfaction\": \"5\",\n    \"financial_stress\": \"1\",\n    \"sleep_duration\": \"1\",\n    \"dietary_habits\": \"0\",\n    \"suicidal_thoughts\": \"0\",\n    \"work_hours\": 7,\n    \"family_history_of_mental_illness\": \"0\"\n}"}, "url": {"raw": "{{api_base}}/predict/", "host": ["{{api_base}}"], "path": ["predict", ""]}}, "response": []}]}, {"name": "User Data", "item": [{"name": "Get User Submissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/submissions/", "host": ["{{api_base}}"], "path": ["submissions", ""]}}, "response": []}]}, {"name": "Admin/Expert", "item": [{"name": "Dashboard Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{api_base}}/dashboard/stats/", "host": ["{{api_base}}"], "path": ["dashboard", "stats", ""]}}, "response": []}, {"name": "Reuse Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{api_base}}/reuse-data/1/", "host": ["{{api_base}}"], "path": ["reuse-data", "1", ""]}}, "response": []}]}, {"name": "HTML Endpoints (Original)", "item": [{"name": "Main Form Page", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/pred/", "host": ["{{base_url}}"], "path": ["pred", ""]}}, "response": []}, {"name": "Submit Form (HTML)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "gender", "value": "1", "type": "text"}, {"key": "age", "value": "25", "type": "text"}, {"key": "work_pressure", "value": "3", "type": "text"}, {"key": "job_satisfaction", "value": "4", "type": "text"}, {"key": "financial_stress", "value": "2", "type": "text"}, {"key": "sleep_duration", "value": "1", "type": "text"}, {"key": "dietary_habits", "value": "0", "type": "text"}, {"key": "suicidal_thoughts", "value": "0", "type": "text"}, {"key": "work_hours", "value": "8", "type": "text"}, {"key": "family_history_of_mental_illness", "value": "0", "type": "text"}]}, "url": {"raw": "{{base_url}}/pred/", "host": ["{{base_url}}"], "path": ["pred", ""]}}, "response": []}, {"name": "History Page", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/pred/history/", "host": ["{{base_url}}"], "path": ["pred", "history", ""]}}, "response": []}, {"name": "Admin Dashboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/pred/admin/dashboard/", "host": ["{{base_url}}"], "path": ["pred", "admin", "dashboard", ""]}}, "response": []}]}]}