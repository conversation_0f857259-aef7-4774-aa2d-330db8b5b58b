# 🧪 API Testing Guide

Quick guide to test your Django Depression Prediction API endpoints.

## 🚀 **Quick Start**

### 1. Start the Django Server
```bash
cd /path/to/your/project
source venv/bin/activate  # if using virtual environment
python manage.py runserver
```

### 2. Create a Test User
```bash
python manage.py createsuperuser
# Follow prompts to create admin user
```

### 3. Test the API

Choose one of these methods:

## 🔧 **Method 1: Python Test Script (Recommended)**

```bash
python test_api_endpoints.py
```

This will automatically test all endpoints and show results.

## 📮 **Method 2: Postman Collection**

1. **Import Collection:**
   - Open Postman
   - Click "Import"
   - Select `Django_Depression_Prediction_API.postman_collection.json`

2. **Set Variables:**
   - Set `base_url` to `http://localhost:8000`

3. **Test Endpoints:**
   - Start with "Health Check"
   - Test "Login" with your created user credentials
   - Try "Predict Depression" endpoints
   - Test other endpoints as needed

## 💻 **Method 3: cURL Commands**

### Quick Health Check
```bash
curl -X GET http://localhost:8000/pred/api/health/
```

### Login
```bash
curl -X POST http://localhost:8000/pred/api/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

### Predict Depression
```bash
curl -X POST http://localhost:8000/pred/api/predict/ \
  -H "Content-Type: application/json" \
  -d '{
    "gender": "1",
    "age": 25,
    "work_pressure": "3",
    "job_satisfaction": "4",
    "financial_stress": "2",
    "sleep_duration": "1",
    "dietary_habits": "0",
    "suicidal_thoughts": "0",
    "work_hours": 8,
    "family_history_of_mental_illness": "0"
  }'
```

### Run All cURL Tests
```bash
./api_test_commands.sh
```

## 📊 **Available Endpoints**

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/pred/api/health/` | GET | Health check | No |
| `/pred/api/login/` | POST | User login | No |
| `/pred/api/logout/` | POST | User logout | No |
| `/pred/api/predict/` | POST | Depression prediction | No |
| `/pred/api/submissions/` | GET | User submissions | Yes |
| `/pred/api/dashboard/stats/` | GET | Dashboard stats | Admin/Expert |
| `/pred/api/reuse-data/{id}/` | POST | Reuse submission data | Expert/Admin |

## 🎯 **Test Data Examples**

### Low Risk Profile
```json
{
    "gender": "1",
    "age": 28,
    "work_pressure": "2",
    "job_satisfaction": "5",
    "financial_stress": "1",
    "sleep_duration": "1",
    "dietary_habits": "0",
    "suicidal_thoughts": "0",
    "work_hours": 7,
    "family_history_of_mental_illness": "0"
}
```

### High Risk Profile
```json
{
    "gender": "0",
    "age": 35,
    "work_pressure": "5",
    "job_satisfaction": "1",
    "financial_stress": "5",
    "sleep_duration": "2",
    "dietary_habits": "2",
    "suicidal_thoughts": "1",
    "work_hours": 12,
    "family_history_of_mental_illness": "1"
}
```

## 🔍 **Field Values Reference**

| Field | Valid Values | Description |
|-------|--------------|-------------|
| gender | "1", "0" | 1=Male, 0=Female |
| age | 18-100 | Age in years |
| work_pressure | "1"-"5" | 1=Very Low, 5=Very High |
| job_satisfaction | "1"-"5" | 1=Very Low, 5=Very High |
| financial_stress | "1"-"5" | 1=Very Low, 5=Very High |
| sleep_duration | "0"-"3" | 0=5-6h, 1=7-8h, 2=<5h, 3=>8h |
| dietary_habits | "0"-"2" | 0=Healthy, 1=Moderate, 2=Unhealthy |
| suicidal_thoughts | "1", "0" | 1=Yes, 0=No |
| work_hours | 1-24 | Hours per day |
| family_history_of_mental_illness | "1", "0" | 1=Yes, 0=No |

## ✅ **Expected Responses**

### Successful Prediction
```json
{
    "success": true,
    "message": "Prediksi berhasil dilakukan",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "submission_id": 123,
        "prediction": {
            "result": "Negatif",
            "probability": 85.5,
            "message": "Model menunjukkan kemungkinan depresi yang lebih rendah..."
        },
        "similarity": {...},
        "form_data_display": {...},
        "submitted_at": "2024-01-15T10:30:00.000Z"
    }
}
```

### Error Response
```json
{
    "success": false,
    "message": "Data validasi gagal",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "errors": {
        "age": ["This field is required."]
    }
}
```

## 🐛 **Troubleshooting**

### Common Issues

1. **Connection Refused**
   - Make sure Django server is running: `python manage.py runserver`
   - Check the URL: `http://localhost:8000`

2. **404 Not Found**
   - Verify the endpoint URL
   - Make sure API URLs are properly configured

3. **401 Unauthorized**
   - Login first using `/pred/api/login/`
   - Check username/password

4. **403 Forbidden**
   - Some endpoints require admin/expert role
   - Create superuser: `python manage.py createsuperuser`

5. **400 Bad Request**
   - Check JSON format
   - Verify all required fields are included
   - Check field value ranges

6. **500 Internal Server Error**
   - Check Django server logs
   - Verify database is accessible
   - Check ML model files exist

### Debug Steps

1. **Check Server Status:**
   ```bash
   curl http://localhost:8000/pred/api/health/
   ```

2. **Check Django Logs:**
   - Look at the terminal where `runserver` is running
   - Check for error messages

3. **Verify Database:**
   ```bash
   python manage.py check
   python manage.py migrate
   ```

4. **Test with Simple Data:**
   ```bash
   curl -X POST http://localhost:8000/pred/api/predict/ \
     -H "Content-Type: application/json" \
     -d '{"gender":"1","age":25,"work_pressure":"3","job_satisfaction":"3","financial_stress":"3","sleep_duration":"1","dietary_habits":"0","suicidal_thoughts":"0","work_hours":8,"family_history_of_mental_illness":"0"}'
   ```

## 📝 **Next Steps**

After successful testing:

1. **Integrate with Frontend:**
   - Use the API endpoints in your web/mobile app
   - Handle authentication and error responses

2. **Production Deployment:**
   - Configure proper authentication
   - Set up HTTPS
   - Add rate limiting
   - Configure logging

3. **Monitoring:**
   - Set up API monitoring
   - Track usage statistics
   - Monitor error rates

## 📚 **Additional Resources**

- **Full API Documentation:** `API_DOCUMENTATION.md`
- **Postman Collection:** `Django_Depression_Prediction_API.postman_collection.json`
- **cURL Test Script:** `api_test_commands.sh`
- **Python Test Script:** `test_api_endpoints.py`

## 🎉 **Success!**

If all tests pass, your API is ready to use! 🚀

The API provides:
- ✅ Depression prediction with ML
- ✅ Indonesian language support
- ✅ Similarity analysis
- ✅ User authentication
- ✅ Admin/Expert features
- ✅ Comprehensive error handling
