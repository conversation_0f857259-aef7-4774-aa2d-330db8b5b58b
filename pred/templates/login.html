<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Masuk - Sistem Prediksi Depresi</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
        }

        .form-section {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input[type="text"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            transform: translateY(-1px);
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            width: 100%;
            margin-bottom: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }

        .link {
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 16px;
            color: #667eea;
            text-decoration: none;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .link:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
        }

        .error {
            color: #dc3545;
            margin-bottom: 20px;
            padding: 12px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .login-container {
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .form-section {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Header Section -->
        <div class="header">
            <h1>🔐 Masuk</h1>
            <p>Akses akun Anda untuk riwayat lengkap</p>
        </div>

        <div class="form-section">
            {% if messages %}
                {% for message in messages %}
                    <div class="error">⚠️ {{ message }}</div>
                {% endfor %}
            {% endif %}

            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <label for="username">👤 Username</label>
                    <input type="text" id="username" name="username" required placeholder="Masukkan username Anda">
                </div>
                <div class="form-group">
                    <label for="password">🔒 Password</label>
                    <input type="password" id="password" name="password" required placeholder="Masukkan password Anda">
                </div>
                <button type="submit" class="submit-btn">🚀 Masuk Sekarang</button>
            </form>

            <div class="links">
                <a href="/pred/register/" class="link">📝 Belum punya akun? Daftar</a>
                <br>
                <a href="/pred/" class="link">🔙 Kembali ke Formulir</a>
            </div>
        </div>
    </div>
</body>
</html>
