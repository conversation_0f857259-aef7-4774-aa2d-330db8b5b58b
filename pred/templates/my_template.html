{# Django Depression Prediction System - Main Form Template #}
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skrining <PERSON><PERSON><PERSON>an <PERSON> - <PERSON><PERSON></title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            min-height: 100vh;
            padding: 20px;
        }

        /* Main Container */
        .main-container {
            max-width: 70%;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* Header Section */
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .header h1 {
            font-size: 2.2em;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* Navigation Bar */
        .nav-bar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: #495057;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .nav-link {
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .nav-link.primary { background: #6366f1; color: white; }
        .nav-link.primary:hover { background: #4f46e5; }
        .nav-link.success { background: #8b5cf6; color: white; }
        .nav-link.success:hover { background: #7c3aed; }
        .nav-link.danger { background: #a855f7; color: white; }
        .nav-link.danger:hover { background: #9333ea; }
        .nav-link.purple { background: #7c3aed; color: white; }
        .nav-link.purple:hover { background: #6d28d9; }

        /* Form Section */
        .form-section {
            padding: 40px;
        }

        .form-title {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .form-title h2 {
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .form-title p {
            color: #6c757d;
            font-size: 1em;
        }

        /* Form Grid */
        .form-grid {
            display: grid;
            gap: 25px;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 0.95em;
            line-height: 1.4;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #6366f1;
            background: white;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            transform: translateY(-1px);
        }

        .form-group select {
            cursor: pointer;
            appearance: none;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%236c757d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        /* Radio Button Styling */
        .radio-group {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 12px;
            margin-top: 8px;
        }

        .radio-group li {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .radio-group input[type="radio"] {
            width: auto;
            margin: 0;
            margin-right: 10px;
            transform: scale(1.2);
            accent-color: #6366f1;
        }

        .radio-group label {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            margin-bottom: 0;
        }

        .radio-group label:hover {
            background: #e9ecef;
            border-color: #6366f1;
            transform: translateY(-1px);
        }

        .radio-group input[type="radio"]:checked + label,
        .radio-group label:has(input[type="radio"]:checked) {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border-color: #6366f1;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .radio-group input[type="radio"]:focus + label,
        .radio-group label:has(input[type="radio"]:focus) {
            outline: none;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        /* Alternative styling for browsers that don't support :has() */
        .radio-option {
            display: flex;
            align-items: center;
            padding: 10px 14px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            margin-bottom: 0;
            flex: 1;
            min-width: fit-content;
            white-space: nowrap;
        }

        .radio-option:hover {
            background: #e9ecef;
            border-color: #6366f1;
            transform: translateY(-1px);
        }

        .radio-option input[type="radio"] {
            width: auto;
            margin: 0;
            margin-right: 12px;
            transform: scale(1.2);
            accent-color: #6366f1;
        }

        .radio-option.selected {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border-color: #6366f1;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        /* Special styling for fields with many options */
        .radio-group.many-options {
            gap: 8px;
        }

        .radio-group.many-options .radio-option {
            flex: 1;
            min-width: 120px;
            padding: 8px 12px;
            font-size: 0.9em;
            text-align: center;
        }

        .radio-group.many-options .radio-option input[type="radio"] {
            margin-right: 6px;
        }



        /* Two-option groups (Yes/No) - Updated with blue/purple theme */
        .radio-group.two-options {
            justify-content: center;
            max-width: 400px;
            margin: 8px auto;
        }

        .radio-group.two-options .radio-option {
            flex: 1;
            max-width: 180px;
            justify-content: center;
            font-weight: 600;
            border-color: #6366f1;
        }

        .radio-group.two-options .radio-option:hover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }

        .radio-group.two-options .radio-option.selected {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            border-color: #4f46e5;
        }

        /* Error Styling */
        .error {
            color: #dc3545;
            font-size: 0.85em;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .error::before {
            content: '⚠️';
            font-size: 0.9em;
        }

        /* Submit Button */
        .submit-section {
            margin-top: 40px;
            text-align: center;
        }

        .submit-btn {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
            min-width: 200px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        /* Responsive Design */
        /* Medium screens - stack some radio groups */
        @media (max-width: 1024px) {
            .radio-group {
                gap: 10px;
            }

            .radio-option {
                padding: 10px 12px;
                font-size: 0.95em;
            }
        }

        /* Tablet screens */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .main-container {
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .nav-bar {
                padding: 15px 20px;
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .nav-links {
                justify-content: center;
            }

            .form-section {
                padding: 30px 20px;
            }

            .form-title h2 {
                font-size: 1.5em;
            }

            /* Stack radio buttons on smaller tablets */
            .radio-group {
                gap: 8px;
                flex-direction: column;
            }

            .radio-option {
                padding: 10px 12px;
                font-size: 0.9em;
                flex: none;
                width: 100%;
            }

            .radio-option input[type="radio"] {
                margin-right: 8px;
                transform: scale(1.1);
            }
        }

        @media (max-width: 480px) {
            .nav-links {
                flex-direction: column;
            }

            .nav-link {
                text-align: center;
                justify-content: center;
            }
        }
    </style>
    <script>
        // JavaScript for radio button interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Handle radio button styling
            const radioGroups = document.querySelectorAll('.radio-group');

            radioGroups.forEach(group => {
                const radioInputs = group.querySelectorAll('input[type="radio"]');
                const labels = group.querySelectorAll('label');

                // Add appropriate class based on number of options
                const optionCount = radioInputs.length;
                if (optionCount === 2) {
                    group.classList.add('two-options');
                } else if (optionCount >= 4) {
                    group.classList.add('many-options');
                }

                // Wrap each radio input and label in a container
                radioInputs.forEach((radio, index) => {
                    const label = labels[index];
                    if (label) {
                        const container = document.createElement('div');
                        container.className = 'radio-option';

                        // Move radio and label into container
                        const parent = radio.parentNode;
                        parent.insertBefore(container, radio);
                        container.appendChild(radio);
                        container.appendChild(label);

                        // Update label to not include radio (since it's now separate)
                        label.innerHTML = label.textContent;

                        // Add click handler
                        container.addEventListener('click', function() {
                            radio.checked = true;
                            updateRadioStyles(group);
                        });

                        // Add change handler
                        radio.addEventListener('change', function() {
                            updateRadioStyles(group);
                        });
                    }
                });

                // Initial styling update
                updateRadioStyles(group);
            });

            function updateRadioStyles(group) {
                const containers = group.querySelectorAll('.radio-option');
                containers.forEach(container => {
                    const radio = container.querySelector('input[type="radio"]');
                    if (radio && radio.checked) {
                        container.classList.add('selected');
                    } else {
                        container.classList.remove('selected');
                    }
                });
            }
        });
    </script>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header">
            <h1>🧠 Skrining Kesehatan Mental</h1>
            <p>Kenali dan Pahami Kesehatan Mental Anda dengan Lebih Baik</p>
        </div>

        <!-- Navigation Bar -->
        <div class="nav-bar">
            {% if is_authenticated %}
                <div class="user-info">
                    <span>👋</span>
                    <span>Selamat datang, {{ user.get_full_name|default:user.username }}!</span>
                </div>
                <div class="nav-links">
                    <a href="/pred/history/" class="nav-link primary">📋 Riwayat Saya</a>
                    {% if is_admin %}
                        <a href="/pred/admin/dashboard/" class="nav-link danger">⚙️ Dashboard Admin</a>
                    {% elif is_expert %}
                        <a href="/pred/admin/dashboard/" class="nav-link success">🔬 Dashboard Ahli</a>
                    {% endif %}
                    <a href="/pred/logout/" class="nav-link danger">🚪 Keluar</a>
                </div>
            {% else %}
                <div class="user-info">
                    <span>🔓</span>
                    <span>Mode Anonim</span>
                </div>
                <div class="nav-links">
                    <a href="/pred/history/" class="nav-link primary">📋 Lihat Riwayat</a>
                    <a href="/pred/login/" class="nav-link success">🔐 Masuk</a>
                    <a href="/pred/register/" class="nav-link purple">📝 Daftar</a>
                </div>
            {% endif %}
        </div>

        <!-- Form Section -->
        <div class="form-section">
            <div class="form-title">
                <h2>🧠 Kenali Kesehatan Mental Anda dengan Lebih Baik</h2>
                <p style="font-size: 1.1em; color: #6366f1; font-weight: 500; margin-bottom: 20px;">
                    Skrining awal untuk membantu Anda memahami kondisi kesehatan mental dan meningkatkan kesadaran diri.
                </p>

                <div style="text-align: left; margin-bottom: 30px; line-height: 1.7; background: #f8f9ff; padding: 25px; border-radius: 15px; border-left: 4px solid #6366f1;">
                    <p style="margin-bottom: 15px;">
                        Selamat datang di sistem skrining kesehatan mental kami. Alat ini dirancang untuk memberikan penilaian awal mengenai kondisi kesehatan mental Anda melalui serangkaian pertanyaan sederhana. Penting untuk diingat bahwa ini bukanlah diagnosis medis final, melainkan langkah pertama untuk membantu Anda memahami kondisi diri sendiri dengan lebih baik.
                    </p>

                    <p style="margin-bottom: 15px;">
                        Dengan menggunakan teknologi AI yang telah teruji, sistem ini dapat membantu meningkatkan kesadaran diri Anda terhadap kesehatan mental. <strong style="color: #6366f1;">Semua informasi yang Anda berikan akan dijaga kerahasiaannya dengan ketat dan hanya digunakan untuk keperluan analisis.</strong> Data Anda aman, terlindungi, dan tidak akan dibagikan kepada pihak ketiga tanpa persetujuan Anda.
                    </p>

                    <p style="color: #8b5cf6; font-weight: 500; font-style: italic; margin: 0;">
                        Silakan isi formulir di bawah ini dengan jujur dan tenang - setiap langkah yang Anda ambil adalah investasi berharga untuk kesehatan mental Anda.
                    </p>
                </div>

                <!-- Progress encouragement -->
                <div class="progress-text" style="text-align: center; margin: 20px 0; color: #6c757d;">
                    <p>📋 Anda sedang mengambil langkah penting untuk kesehatan mental Anda</p>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}

                <div class="form-grid">
                    <!-- Personal Information Section -->
                    <div class="form-group">
                        <label for="gender">👤 Jenis Kelamin</label>
                        {{ form.gender }}
                        {% if form.gender.errors %}
                            <div class="error">{{ form.gender.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="age">🎂 Usia (tahun)</label>
                        {{ form.age }}
                        {% if form.age.errors %}
                            <div class="error">{{ form.age.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- Work-Related Questions -->
                    <div class="form-group">
                        <label for="work_pressure">💼 Bagaimana Anda menilai <strong>tingkat tekanan</strong> dalam <strong>pekerjaan</strong> Anda akhir-akhir ini?</label>
                        {{ form.work_pressure }}
                        {% if form.work_pressure.errors %}
                            <div class="error">{{ form.work_pressure.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="job_satisfaction">😊 Secara keseluruhan, seberapa <strong>puas</strong> Anda dengan pekerjaan Anda saat ini?</label>
                        {{ form.job_satisfaction }}
                        {% if form.job_satisfaction.errors %}
                            <div class="error">{{ form.job_satisfaction.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="work_hours">⏰ Rata-rata, berapa <strong>jam kerja</strong> yang Anda habiskan dalam sehari?</label>
                        {{ form.work_hours }}
                        {% if form.work_hours.errors %}
                            <div class="error">{{ form.work_hours.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- Financial and Lifestyle Questions -->
                    <div class="form-group">
                        <label for="financial_stress">💰 Bagaimana perasaan Anda mengenai <strong>kondisi keuangan</strong> Anda saat ini?</label>
                        {{ form.financial_stress }}
                        {% if form.financial_stress.errors %}
                            <div class="error">{{ form.financial_stress.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="sleep_duration">😴 Rata-rata, berapa <strong>jam tidur</strong> Anda dalam semalam?</label>
                        {{ form.sleep_duration }}
                        {% if form.sleep_duration.errors %}
                            <div class="error">{{ form.sleep_duration.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="dietary_habits">🍽️ Bagaimana Anda menggambarkan <strong>pola makan</strong> Anda sehari-hari?</label>
                        {{ form.dietary_habits }}
                        {% if form.dietary_habits.errors %}
                            <div class="error">{{ form.dietary_habits.errors }}</div>
                        {% endif %}
                    </div>

                    <!-- Mental Health Questions -->
                    <div class="form-group">
                        <label for="suicidal_thoughts">🧠 Apakah Anda pernah memiliki <strong>pikiran untuk bunuh diri</strong>?</label>
                        {{ form.suicidal_thoughts }}
                        {% if form.suicidal_thoughts.errors %}
                            <div class="error">{{ form.suicidal_thoughts.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="family_history_of_mental_illness">👨‍👩‍👧‍👦 Apakah ada anggota keluarga Anda yang memiliki <strong>riwayat gangguan kesehatan mental</strong>?</label>
                        {{ form.family_history_of_mental_illness }}
                        {% if form.family_history_of_mental_illness.errors %}
                            <div class="error">{{ form.family_history_of_mental_illness.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Completion Encouragement -->
                <div class="completion-encouragement" style="background: #f0f9ff; padding: 20px; border-radius: 15px; margin: 30px 0; text-align: center; border: 1px solid #e0e7ff;">
                    <p style="color: #0369a1; margin: 0; font-size: 1.05em; line-height: 1.6;">
                        🌟 <strong>Terima kasih telah meluangkan waktu untuk diri Anda sendiri.</strong><br>
                        <span style="font-weight: normal;">Hasil skrining akan membantu Anda memahami kondisi kesehatan mental dengan lebih baik.</span>
                    </p>
                </div>

                <!-- Submit Section -->
                <div class="submit-section">
                    <button type="submit" class="submit-btn">
                        🚀 Analisis Kesehatan Mental Saya
                    </button>
                    <p style="margin-top: 15px; color: #6c757d; font-size: 0.9em;">
                        🔒 Data Anda akan diproses dengan aman dan rahasia
                    </p>
                </div>
            </form>
        </div>

        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 0.9em; border-top: 1px solid #e9ecef;">
            <p>🔒 Sistem ini menggunakan teknologi AI untuk memberikan penilaian awal kesehatan mental</p>
            <p style="margin-top: 5px;">⚠️ Hasil ini bukan pengganti konsultasi dengan profesional kesehatan mental</p>
        </div>
    </div>
</body>
</html>