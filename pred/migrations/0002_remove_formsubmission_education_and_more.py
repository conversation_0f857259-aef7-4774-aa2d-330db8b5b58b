# Generated by Django 6.0.dev20250709161004 on 2025-07-14 19:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pred', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='formsubmission',
            name='education',
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='dietary_habits',
            field=models.CharField(default='0', max_length=10, verbose_name='Dietary Habits'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='family_history_of_mental_illness',
            field=models.CharField(default='0', max_length=10, verbose_name='Family History of Mental Illness'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='financial_stress',
            field=models.CharField(default='1', max_length=10, verbose_name='Financial Stress'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='job_satisfaction',
            field=models.CharField(default='1', max_length=10, verbose_name='Job Satisfaction'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='sleep_duration',
            field=models.CharField(default='2', max_length=10, verbose_name='Sleep Duration'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='suicidal_thoughts',
            field=models.CharField(default=1, max_length=10, verbose_name='Suicidal Thoughts'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='work_hours',
            field=models.IntegerField(default='0', verbose_name='Work Hours'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='work_pressure',
            field=models.CharField(default=40, max_length=10, verbose_name='Work Pressure'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='formsubmission',
            name='age',
            field=models.IntegerField(verbose_name='Age'),
        ),
        migrations.AlterField(
            model_name='formsubmission',
            name='gender',
            field=models.CharField(max_length=10, verbose_name='Gender'),
        ),
    ]
