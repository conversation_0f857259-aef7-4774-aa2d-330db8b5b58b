# Generated by Django 6.0.dev20250709161004 on 2025-07-15 15:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pred', '0005_formsubmission_similar_case_id_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='formsubmission',
            name='is_reused_in_dataset',
            field=models.BooleanField(default=False, verbose_name='Reused in Dataset'),
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='reused_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Reused At'),
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='reused_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reused_submissions', to=settings.AUTH_USER_MODEL, verbose_name='Reused By'),
        ),
    ]
