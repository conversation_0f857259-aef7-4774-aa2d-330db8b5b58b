# Generated by Django 6.0.dev20250709161004 on 2025-07-14 17:48

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FormSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, verbose_name='Nama')),
                ('email', models.EmailField(max_length=254, verbose_name='Email')),
                ('gender', models.<PERSON>r<PERSON><PERSON>(max_length=10, verbose_name='<PERSON><PERSON>')),
                ('age', models.<PERSON><PERSON><PERSON><PERSON>(max_length=10, verbose_name='Usia')),
                ('education', models.Char<PERSON><PERSON>(max_length=20, verbose_name='Pendidikan')),
                ('submitted_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Waktu Submit')),
            ],
            options={
                'verbose_name': 'Form Submission',
                'verbose_name_plural': 'Form Submissions',
                'ordering': ['-submitted_at'],
            },
        ),
    ]
