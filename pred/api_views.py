"""
REST API Views for Django Depression Prediction System
Provides JSON-based API endpoints for all major functionality
"""

import json
import logging
from datetime import datetime
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User, Group
from django.contrib.auth.decorators import login_required
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from .models import FormSubmission
from .forms import MyForm
from .views import (
    is_admin, is_expert, has_admin_access,
    get_gender_display, get_pressure_display, get_sleep_duration_display,
    get_dietary_habits_display, get_yes_no_display,
    predict_depression, find_similar_cases
)

logger = logging.getLogger(__name__)

def api_response(success=True, data=None, message="", status=200):
    """Standard API response format"""
    response_data = {
        'success': success,
        'message': message,
        'timestamp': datetime.now().isoformat(),
        'data': data or {}
    }
    return JsonResponse(response_data, status=status)

def api_error(message, status=400, errors=None):
    """Standard API error response"""
    response_data = {
        'success': False,
        'message': message,
        'timestamp': datetime.now().isoformat(),
        'errors': errors or {}
    }
    return JsonResponse(response_data, status=status)

@csrf_exempt
@require_http_methods(["POST"])
def api_predict_depression(request):
    """
    API endpoint for depression prediction
    POST /pred/api/predict/
    """
    try:
        # Parse JSON data
        data = json.loads(request.body)

        # Log the API request
        print("=" * 60)
        print("🔍 API REQUEST LOG")
        print("=" * 60)
        print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Method: {request.method}")
        print(f"📍 Path: {request.path}")
        print(f"🖥️  User Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")
        print(f"📡 Remote IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
        print(f"📦 Content Type: {request.META.get('CONTENT_TYPE', 'Unknown')}")

        print("\n📦 Raw JSON Data:")
        print(json.dumps(data, indent=2, ensure_ascii=False))

        # Validate form data
        form = MyForm(data)
        if not form.is_valid():
            print("\n❌ Form Validation: FAILED")
            print("\n🚨 Form Errors:")
            for field, errors in form.errors.items():
                print(f"  {field}: {errors}")
            print("=" * 60)
            return api_error("Data validasi gagal", status=400, errors=form.errors)

        print("\n✅ Form Validation: PASSED")
        print("\n🧹 Cleaned Form Data:")
        for key, value in form.cleaned_data.items():
            print(f"  {key}: {value}")
        print("=" * 60)
        
        # Get cleaned data
        form_data = form.cleaned_data
        
        # Perform ML prediction
        try:
            prediction_result = predict_depression(form_data)
        except Exception as e:
            logger.error(f"ML prediction failed: {str(e)}")
            return api_error("Prediksi ML gagal", status=500)

        # Perform similarity analysis
        print("\n🔍 API: Starting similarity analysis...")
        try:
            similarity_result = find_similar_cases(form_data)
            print(f"✅ API: Similarity analysis completed: {similarity_result.get('success', False) if similarity_result else False}")
            if similarity_result and not similarity_result.get('success', False):
                print(f"❌ API: Similarity analysis failed: {similarity_result.get('message', 'Unknown error')}")
        except Exception as e:
            logger.warning(f"Similarity analysis failed: {str(e)}")
            print(f"❌ API: Similarity analysis exception: {str(e)}")
            similarity_result = {
                'success': False,
                'error': str(e),
                'message': 'Terjadi kesalahan dalam analisis kemiripan'
            }
        
        # Save to database
        try:
            submission = FormSubmission.objects.create(
                user=request.user if request.user.is_authenticated else None,
                gender=form_data['gender'],
                age=form_data['age'],
                work_pressure=form_data['work_pressure'],
                job_satisfaction=form_data['job_satisfaction'],
                financial_stress=form_data['financial_stress'],
                sleep_duration=form_data['sleep_duration'],
                dietary_habits=form_data['dietary_habits'],
                suicidal_thoughts=form_data['suicidal_thoughts'],
                work_hours=form_data['work_hours'],
                family_history_of_mental_illness=form_data['family_history_of_mental_illness'],
                prediction_result=prediction_result.get('prediction'),
                prediction_probability=prediction_result.get('probability'),
                prediction_message=prediction_result.get('message'),
                similarity_score=similarity_result.get('best_similarity_score') if similarity_result else None,
                similar_case_id=similarity_result.get('best_match_index') if similarity_result else None,
            )
        except Exception as e:
            logger.error(f"Database save failed: {str(e)}")
            return api_error("Gagal menyimpan data", status=500)
        
        # Prepare response data with Indonesian display values
        response_data = {
            'submission_id': submission.id,
            'prediction': {
                'result': prediction_result.get('prediction'),
                'probability': prediction_result.get('probability'),
                'message': prediction_result.get('message')
            },
            'similarity': similarity_result,
            'form_data_display': {
                'jenis_kelamin': get_gender_display(form_data['gender']),
                'usia': form_data['age'],
                'tekanan_kerja': get_pressure_display(form_data['work_pressure']),
                'kepuasan_kerja': get_pressure_display(form_data['job_satisfaction']),
                'stres_keuangan': get_pressure_display(form_data['financial_stress']),
                'durasi_tidur': get_sleep_duration_display(form_data['sleep_duration']),
                'kebiasaan_makan': get_dietary_habits_display(form_data['dietary_habits']),
                'pikiran_bunuh_diri': get_yes_no_display(form_data['suicidal_thoughts']),
                'jam_kerja': form_data['work_hours'],
                'riwayat_keluarga': get_yes_no_display(form_data['family_history_of_mental_illness'])
            },
            'submitted_at': submission.submitted_at.isoformat()
        }
        
        return api_response(
            success=True,
            data=response_data,
            message="Prediksi berhasil dilakukan"
        )
        
    except json.JSONDecodeError:
        return api_error("Format JSON tidak valid", status=400)
    except Exception as e:
        logger.error(f"API predict error: {str(e)}")
        return api_error("Terjadi kesalahan server", status=500)

@csrf_exempt
@require_http_methods(["POST"])
def api_login(request):
    """
    API endpoint for user login
    POST /pred/api/login/
    """
    try:
        data = json.loads(request.body)
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return api_error("Username dan password diperlukan", status=400)
        
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            
            # Get user role
            user_role = 'admin' if is_admin(user) else 'expert' if is_expert(user) else 'user'
            
            return api_response(
                success=True,
                data={
                    'user_id': user.id,
                    'username': user.username,
                    'full_name': user.get_full_name(),
                    'email': user.email,
                    'role': user_role,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser
                },
                message="Login berhasil"
            )
        else:
            return api_error("Username atau password salah", status=401)
            
    except json.JSONDecodeError:
        return api_error("Format JSON tidak valid", status=400)
    except Exception as e:
        logger.error(f"API login error: {str(e)}")
        return api_error("Terjadi kesalahan server", status=500)

@csrf_exempt
@require_http_methods(["POST"])
def api_logout(request):
    """
    API endpoint for user logout
    POST /pred/api/logout/
    """
    try:
        logout(request)
        return api_response(
            success=True,
            message="Logout berhasil"
        )
    except Exception as e:
        logger.error(f"API logout error: {str(e)}")
        return api_error("Terjadi kesalahan server", status=500)

@login_required
@require_http_methods(["GET"])
def api_user_submissions(request):
    """
    API endpoint to get user's submissions history
    GET /pred/api/submissions/
    """
    try:
        # Get user role
        user_is_admin = is_admin(request.user)
        user_is_expert = is_expert(request.user)
        
        # Determine which submissions to show
        if user_is_admin or user_is_expert:
            submissions = FormSubmission.objects.all().order_by('-submitted_at')
        else:
            # Regular users see only their own submissions
            submissions = FormSubmission.objects.filter(user=request.user).order_by('-submitted_at')
        
        # Prepare submissions data
        submissions_data = []
        for submission in submissions:
            submission_data = {
                'id': submission.id,
                'form_data': {
                    'jenis_kelamin': get_gender_display(submission.gender),
                    'usia': submission.age,
                    'tekanan_kerja': get_pressure_display(submission.work_pressure),
                    'kepuasan_kerja': get_pressure_display(submission.job_satisfaction),
                    'stres_keuangan': get_pressure_display(submission.financial_stress),
                    'durasi_tidur': get_sleep_duration_display(submission.sleep_duration),
                    'kebiasaan_makan': get_dietary_habits_display(submission.dietary_habits),
                    'pikiran_bunuh_diri': get_yes_no_display(submission.suicidal_thoughts),
                    'jam_kerja': submission.work_hours,
                    'riwayat_keluarga': get_yes_no_display(submission.family_history_of_mental_illness)
                },
                'prediction': {
                    'result': submission.prediction_result,
                    'probability': submission.prediction_probability,
                    'message': submission.prediction_message
                },
                'similarity': {
                    'score': submission.similarity_score,
                    'similar_case_id': submission.similar_case_id
                },
                'reuse_info': {
                    'is_reused': submission.is_reused_in_dataset,
                    'reused_at': submission.reused_at.isoformat() if submission.reused_at else None,
                    'reused_by': submission.reused_by.username if submission.reused_by else None
                },
                'submitted_at': submission.submitted_at.isoformat()
            }
            submissions_data.append(submission_data)
        
        return api_response(
            success=True,
            data={
                'submissions': submissions_data,
                'total_count': len(submissions_data),
                'user_role': 'admin' if user_is_admin else 'expert' if user_is_expert else 'user'
            },
            message="Data riwayat berhasil diambil"
        )
        
    except Exception as e:
        logger.error(f"API submissions error: {str(e)}")
        return api_error("Terjadi kesalahan server", status=500)

@login_required
@require_http_methods(["GET"])
def api_dashboard_stats(request):
    """
    API endpoint for dashboard statistics
    GET /pred/api/dashboard/stats/
    """
    try:
        if not has_admin_access(request.user):
            return api_error("Akses ditolak", status=403)
        
        # Get statistics
        total_submissions = FormSubmission.objects.count()
        total_users = User.objects.count()
        reused_submissions = FormSubmission.objects.filter(is_reused_in_dataset=True).count()
        
        # Get recent submissions
        recent_submissions = FormSubmission.objects.order_by('-submitted_at')[:5]
        recent_data = []
        for submission in recent_submissions:
            recent_data.append({
                'id': submission.id,
                'gender': get_gender_display(submission.gender),
                'age': submission.age,
                'prediction_result': submission.prediction_result,
                'submitted_at': submission.submitted_at.isoformat()
            })
        
        return api_response(
            success=True,
            data={
                'statistics': {
                    'total_submissions': total_submissions,
                    'total_users': total_users,
                    'reused_submissions': reused_submissions
                },
                'recent_submissions': recent_data,
                'user_role': 'admin' if is_admin(request.user) else 'expert'
            },
            message="Statistik dashboard berhasil diambil"
        )
        
    except Exception as e:
        logger.error(f"API dashboard stats error: {str(e)}")
        return api_error("Terjadi kesalahan server", status=500)

@login_required
@csrf_exempt
@require_http_methods(["POST"])
def api_reuse_data(request, submission_id):
    """
    API endpoint for expert to reuse submission data
    POST /pred/api/reuse-data/<int:submission_id>/
    """
    try:
        if not is_expert(request.user) and not is_admin(request.user):
            return api_error("Akses ditolak - hanya untuk Expert/Admin", status=403)
        
        # Get submission
        try:
            submission = FormSubmission.objects.get(id=submission_id)
        except FormSubmission.DoesNotExist:
            return api_error(f"Pengiriman #{submission_id} tidak ditemukan", status=404)
        
        # Check if already reused
        if submission.is_reused_in_dataset:
            return api_error(
                f"Pengiriman #{submission_id} sudah ditambahkan ke dataset pada {submission.reused_at.strftime('%Y-%m-%d %H:%M')} oleh {submission.reused_by.username if submission.reused_by else 'Tidak Diketahui'}",
                status=400
            )
        
        # Mark as reused
        submission.is_reused_in_dataset = True
        submission.reused_at = datetime.now()
        submission.reused_by = request.user
        submission.save()
        
        return api_response(
            success=True,
            data={
                'submission_id': submission_id,
                'reused_at': submission.reused_at.isoformat(),
                'reused_by': request.user.username
            },
            message=f"Berhasil menambahkan data pengiriman #{submission_id} ke dataset"
        )
        
    except Exception as e:
        logger.error(f"API reuse data error: {str(e)}")
        return api_error("Terjadi kesalahan server", status=500)

@require_http_methods(["GET"])
def api_health_check(request):
    """
    API endpoint for health check
    GET /pred/api/health/
    """
    try:
        # Basic health checks
        db_status = "OK"
        try:
            FormSubmission.objects.count()
        except Exception:
            db_status = "ERROR"
        
        return api_response(
            success=True,
            data={
                'status': 'healthy',
                'database': db_status,
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            },
            message="Sistem berjalan normal"
        )
        
    except Exception as e:
        logger.error(f"API health check error: {str(e)}")
        return api_error("Terjadi kesalahan server", status=500)
