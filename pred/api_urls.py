"""
API URL Configuration for Django Depression Prediction System
REST API endpoints for all major functionality
"""

from django.urls import path
from . import api_views

app_name = 'pred_api'

urlpatterns = [
    # Health check
    path('health/', api_views.api_health_check, name='api_health_check'),
    
    # Authentication endpoints
    path('login/', api_views.api_login, name='api_login'),
    path('logout/', api_views.api_logout, name='api_logout'),
    
    # Main prediction endpoint
    path('predict/', api_views.api_predict_depression, name='api_predict_depression'),
    
    # User data endpoints
    path('submissions/', api_views.api_user_submissions, name='api_user_submissions'),
    
    # Admin/Expert endpoints
    path('dashboard/stats/', api_views.api_dashboard_stats, name='api_dashboard_stats'),
    path('reuse-data/<int:submission_id>/', api_views.api_reuse_data, name='api_reuse_data'),
]
