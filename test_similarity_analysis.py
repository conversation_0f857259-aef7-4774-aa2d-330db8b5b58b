#!/usr/bin/env python3
"""
Test script to verify similarity analysis is working correctly
"""

import os
import sys
import django

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mysite.settings')
django.setup()

from pred.views import find_similar_cases, transform_form_data_for_model
import pandas as pd
from django.conf import settings

def test_dataset_loading():
    """Test if dataset can be loaded properly"""
    print("📊 Testing Dataset Loading")
    print("=" * 40)
    
    dataset_path = os.path.join(settings.BASE_DIR, 'dataset_processed.csv')
    print(f"Dataset path: {dataset_path}")
    print(f"File exists: {os.path.exists(dataset_path)}")
    
    if os.path.exists(dataset_path):
        try:
            df = pd.read_csv(dataset_path)
            print(f"✅ Dataset loaded successfully")
            print(f"Rows: {len(df)}")
            print(f"Columns: {list(df.columns)}")
            
            # Check for gender columns
            if 'gender' in df.columns:
                print("📝 Dataset has 'gender' column (old format)")
                print(f"Gender values: {df['gender'].unique()}")
            
            if 'gender_Male' in df.columns and 'gender_Female' in df.columns:
                print("📝 Dataset has 'gender_Male' and 'gender_Female' columns (new format)")
                print(f"Gender_Male values: {df['gender_Male'].unique()}")
                print(f"Gender_Female values: {df['gender_Female'].unique()}")
            
            # Show first few rows
            print("\n📋 First 3 rows:")
            print(df.head(3))
            
            return True, df
            
        except Exception as e:
            print(f"❌ Error loading dataset: {str(e)}")
            return False, None
    else:
        print("❌ Dataset file not found")
        return False, None

def test_data_transformation():
    """Test data transformation function"""
    print("\n🔄 Testing Data Transformation")
    print("=" * 40)
    
    # Test data from your example
    test_form_data = {
        'gender': '1',
        'age': 20,
        'work_pressure': '2',
        'job_satisfaction': '1',
        'financial_stress': '4',
        'sleep_duration': '2',
        'dietary_habits': '1',
        'suicidal_thoughts': '1',
        'work_hours': 10,
        'family_history_of_mental_illness': '1'
    }
    
    print("Original form data:")
    for key, value in test_form_data.items():
        print(f"  {key}: {value}")
    
    try:
        transformed = transform_form_data_for_model(test_form_data)
        print("\nTransformed data:")
        for key, value in transformed.items():
            print(f"  {key}: {value}")
        
        print("✅ Data transformation successful")
        return True, transformed
        
    except Exception as e:
        print(f"❌ Data transformation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_similarity_calculation():
    """Test similarity calculation"""
    print("\n🔍 Testing Similarity Calculation")
    print("=" * 40)
    
    # Test data from your example
    test_form_data = {
        'gender': '1',
        'age': 20,
        'work_pressure': '2',
        'job_satisfaction': '1',
        'financial_stress': '4',
        'sleep_duration': '2',
        'dietary_habits': '1',
        'suicidal_thoughts': '1',
        'work_hours': 10,
        'family_history_of_mental_illness': '1'
    }
    
    print("Testing similarity with form data:")
    for key, value in test_form_data.items():
        print(f"  {key}: {value}")
    
    try:
        similarity_result = find_similar_cases(test_form_data)
        
        print(f"\nSimilarity calculation result:")
        print(f"Success: {similarity_result.get('success', False)}")
        
        if similarity_result.get('success', False):
            print(f"✅ Similarity calculation successful")
            print(f"Best similarity: {similarity_result.get('best_similarity', 0):.2f}%")
            print(f"Total cases analyzed: {similarity_result.get('total_cases', 0)}")
            
            # Show best match
            if 'best_match' in similarity_result:
                print(f"\n🎯 Best Match:")
                best_match = similarity_result['best_match']
                for key, value in best_match.items():
                    print(f"  {key}: {value}")
            
            # Show top matches
            if 'top_matches' in similarity_result:
                print(f"\n🏆 Top 3 Matches:")
                for i, match in enumerate(similarity_result['top_matches'][:3], 1):
                    print(f"  {i}. Similarity: {match['similarity']:.2f}% - {match['gender']}, Age {match['age']}")
            
            return True, similarity_result
        else:
            print(f"❌ Similarity calculation failed")
            print(f"Error: {similarity_result.get('error', 'Unknown error')}")
            print(f"Message: {similarity_result.get('message', 'No message')}")
            return False, similarity_result
            
    except Exception as e:
        print(f"❌ Similarity calculation exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_different_profiles():
    """Test similarity with different user profiles"""
    print("\n👥 Testing Different User Profiles")
    print("=" * 40)
    
    profiles = [
        {
            "name": "Low Risk Male",
            "data": {
                'gender': '1', 'age': 25, 'work_pressure': '1', 'job_satisfaction': '5',
                'financial_stress': '1', 'sleep_duration': '1', 'dietary_habits': '0',
                'suicidal_thoughts': '0', 'work_hours': 7, 'family_history_of_mental_illness': '0'
            }
        },
        {
            "name": "High Risk Female",
            "data": {
                'gender': '0', 'age': 35, 'work_pressure': '5', 'job_satisfaction': '1',
                'financial_stress': '5', 'sleep_duration': '2', 'dietary_habits': '2',
                'suicidal_thoughts': '1', 'work_hours': 12, 'family_history_of_mental_illness': '1'
            }
        }
    ]
    
    for profile in profiles:
        print(f"\n📋 Testing: {profile['name']}")
        
        try:
            result = find_similar_cases(profile['data'])
            if result.get('success', False):
                print(f"  ✅ Success - Best similarity: {result.get('best_similarity', 0):.2f}%")
                if 'best_match' in result:
                    best = result['best_match']
                    print(f"  🎯 Best match: {best['gender']}, Age {best['age']}, Depression: {best['depression']}")
            else:
                print(f"  ❌ Failed: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"  ❌ Exception: {str(e)}")

def main():
    """Run all similarity tests"""
    print("🔍 Similarity Analysis Test Suite")
    print("=" * 50)
    print("Testing the similarity analysis functionality")
    print()
    
    try:
        # Test dataset loading
        dataset_ok, df = test_dataset_loading()
        if not dataset_ok:
            print("❌ Cannot proceed without dataset")
            return False
        
        # Test data transformation
        transform_ok, transformed = test_data_transformation()
        if not transform_ok:
            print("❌ Cannot proceed without data transformation")
            return False
        
        # Test similarity calculation
        similarity_ok, similarity_result = test_similarity_calculation()
        
        # Test different profiles
        test_different_profiles()
        
        print("\n" + "=" * 50)
        if similarity_ok:
            print("🎉 Similarity analysis is working correctly!")
            print("✅ Dataset loads properly")
            print("✅ Data transformation works")
            print("✅ Similarity calculation succeeds")
            print("✅ Results are properly formatted")
            
            print("\n📋 Summary:")
            print("- Similarity analysis should now display in the web interface")
            print("- Check the results page after form submission")
            print("- Look for the similarity section with best match and top matches")
            
            return True
        else:
            print("❌ Similarity analysis has issues!")
            print("- Check the error messages above")
            print("- Verify dataset file exists and has correct format")
            print("- Check server logs for detailed error information")
            return False
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
